%% MOPSO_GridOptimizer.m - 多目标PSO算法优化网格参数
% 该文件实现MOPSO算法优化网格参数，使用Pareto最优概念处理多目标优化问题
% 与原PSO_GridOptimizer保持相同的接口，便于集成

function [best_grid_params, pso_history] = MOPSO_GridOptimizer(pso_params, grid_params_range, ...
    depth, rFair, zFair, angles_mat, target_displacement, ...
    constant_force, test_angles, num_directions, typeName, ... % Keep typeName for MooringOptimizer compatibility
    main_wind_dir, max_grid_adjustments, grid_adjustment_factor, mooring_params, ...
    boundary, wind_params, upper_weight, lower_weight, show_plots, ... % 添加上下层适应度比例系数
    use_multi_direction, wind_directions, enable_plot_saving) % 添加多风向分析参数和图形保存控制

    %% MOPSO_GridOptimizer - 使用多目标粒子群优化算法优化网格参数
    %
    % 输入:
    %   pso_params - PSO算法参数结构体
    %   grid_params_range - 网格参数范围结构体
    %   ... (其他从 Main_Controller 传入的参数) ...
    %   boundary - 场地边界坐标
    %   wind_params - 风参数结构体
    %   upper_weight - 上层PSO适应度比例系数 (默认为1.0，MOPSO中用于兼容性)
    %   lower_weight - 下层系泊优化适应度比例系数 (默认为1.0，MOPSO中用于兼容性)
    %   show_plots - 是否显示并更新实时图形（default: true）
    %   use_multi_direction - 是否使用多风向分析
    %   wind_directions - 风向角度数组 [deg]
    %   enable_plot_saving - 是否保存图形到文件（default: true）
    %
    % 输出:
    %   best_grid_params - 最优网格参数结构体
    %   pso_history - MOPSO优化历史记录，与单目标PSO不同，结构包含:
    %     - iteration: 迭代次数
    %     - pareto_front: 每次迭代的Pareto前沿解集
    %     - pareto_metrics: 性能指标(超体积、扩展度、均匀度)
    %     - complete_analysis: {cell array} 存储当前迭代中*所有*粒子评估的详细分析结果
    %       (每个 cell 内的结构体包含新增的 'mooring_line_forces_details' 字段)
    %     - all_pareto_fronts: 所有迭代的Pareto前沿(在最后一个记录中)
    %     - all_metrics: 所有迭代的性能指标(在最后一个记录中)
    %
    % 注意：返回的pso_history结构与传统PSO不同，请使用专门的MOPSO结果处理函数

    %% 添加MOPSO工具函数路径
    addpath(genpath('MOPSO'));

    %% 1. 参数提取与初始化
    % --- 参数提取 ---
    swarm_size = pso_params.swarm_size;
    max_iterations = pso_params.max_iterations;
    c1 = pso_params.c1;
    c2 = pso_params.c2;
    w_start = pso_params.w_start;
    w_end = pso_params.w_end;

    length_min = grid_params_range.length_min;
    length_max = grid_params_range.length_max;
    rotation_min = grid_params_range.rotation_min;
    rotation_max = grid_params_range.rotation_max;

    % --- 新增常量定义 ---
    required_min_sf = 1.67; % 所需最小安全系数
    sf_penalty_coefficient = 1e5; % 安全系数惩罚系数
    disp_penalty_coefficient = 1e5; % 位移惩罚系数
    mooring_failure_penalty_coefficient = 1e5; % 系泊优化失败惩罚系数
    epsilon = 1e-6; % 防止除以零的小值

    % --- 新增：并行池检查与启动 ---
    pool = gcp('nocreate'); % 获取当前并行池，如果不存在则返回空
    if isempty(pool)
        fprintf('未检测到活动的并行池，尝试启动默认并行池...\n');
        try
            parpool; % 启动默认并行池
            pool = gcp;
            fprintf('并行池启动成功，使用 %d 个工作进程。\n', pool.NumWorkers);
        catch parpool_err
            fprintf('警告: 无法启动并行池: %s\n', parpool_err.message);
            fprintf('将继续以串行模式运行。\n');
            % 在这种情况下，后续的parfor会自动退化为for循环
        end
    else
        fprintf('检测到活动的并行池，使用 %d 个工作进程。\n', pool.NumWorkers);
    end
    % --- 并行池检查结束 ---

    % --- MOPSO特定参数 ---
    archive_max_size = min(100, swarm_size * 2); % 存档最大容量
    leader_selection_method = 'crowding'; % 引导者选择方法：'random', 'crowding', 'binary'

    % 从wind_params中提取min_turbine_distance
    if isfield(wind_params, 'min_turbine_distance') && ~isempty(wind_params.min_turbine_distance)
        min_turbine_distance = wind_params.min_turbine_distance;
        fprintf('使用从wind_params传入的最小风机间距: %.2f m\n', min_turbine_distance);
    else
        min_turbine_distance = 1000; % 默认值
        fprintf('警告: wind_params中未包含min_turbine_distance，使用默认值%.2f m\n', min_turbine_distance);
    end

    % 增加参数有效性检查
    if ~isfinite(min_turbine_distance) || min_turbine_distance <= 0
        fprintf('警告: min_turbine_distance值无效(%.2f)，使用默认值1000m\n', min_turbine_distance);
        min_turbine_distance = 1000; % 回退到默认值
    end

    % 添加与风机直径的比例检查
    if isfield(wind_params, 'rotor_diameter') && ~isempty(wind_params.rotor_diameter)
        min_recommended = wind_params.rotor_diameter * 2; % 最小推荐间距为2倍直径
        if min_turbine_distance < min_recommended
            fprintf('警告: min_turbine_distance(%.2f m)小于推荐最小值(%.2f m，为风轮直径的2倍)\n', min_turbine_distance, min_recommended);
        end
    end

    % 设置默认的适应度比例系数
    if nargin < 17 || isempty(upper_weight)
        upper_weight = 1.0; % 默认上层适应度比例系数
    end

    if nargin < 18 || isempty(lower_weight)
        lower_weight = 1.0; % 默认下层适应度比例系数
    end

    if nargin < 19 || isempty(show_plots)
        show_plots = true;
    end

    % 设置多风向分析参数
    if nargin < 20 || isempty(use_multi_direction)
        use_multi_direction = false; % 默认不使用多风向分析
    end

    if nargin < 21 || isempty(wind_directions)
        wind_directions = 0:30:330; % 默认每30度一个方向，共12个方向
    end

    if nargin < 22 || isempty(enable_plot_saving)
        enable_plot_saving = true; % 默认启用图形保存
    end

    fprintf('MOPSO多目标粒子群优化启动 - 上层: %.2f, 下层: %.2f\n', upper_weight, lower_weight);
    fprintf('实时图形显示: %s\n', conditional_str(show_plots, '开启', '关闭'));
    fprintf('多风向锚点受力分析: %s\n', conditional_str(use_multi_direction, '开启', '关闭'));
    if use_multi_direction
        fprintf('分析风向: %s\n', mat2str(wind_directions));
    end

    % --- 新增：创建本次运行的结果子目录 ---
    resultsBaseDir = 'results';
    timestamp = datestr(now, 'yyyymmdd_HHMMSS');
    % 使用传入的参数创建目录名
    runDirName = sprintf('[q(%.1f),w(%.1f)]%s', min_turbine_distance, length_min, timestamp);
    runResultsPath = fullfile(resultsBaseDir, runDirName);
    if ~exist(runResultsPath, 'dir')
        try
            mkdir(runResultsPath);
            fprintf('为本次运行创建结果目录: %s\n', runResultsPath);
        catch mkdir_err
            fprintf('警告: 无法创建结果目录 %s: %s\n', runResultsPath, mkdir_err.message);
            runResultsPath = resultsBaseDir; % 回退到基础目录
        end
    else
        fprintf('结果目录已存在: %s\n', runResultsPath);
    end
    % --- 目录创建结束 ---

    % 成本计算参数 (应从 Main_Controller 传入)
    costParams = struct(...
        'chain_cost_coef', 0.055, ... % 元/mm^2/m
        'currency_rmb', 7.0, ...
        'currency_usd_million', 1e6, ...
        'default_anchor_cost', 0.5, ... % 百万美元
        'default_chain_cost', 0.3, ... % 百万美元
        'default_chain_diameter', 120, ... % 默认锚链直径 (mm)
        'default_water_depth', depth, ... % 使用传入的水深参数
        'default_lines_per_turbine', 3, ... % 每个风机的系泊线数量
        'default_line_depth_ratio', 3, ... % 系泊线长度与水深的比例
        'default_num_turbines', 0 ... % 初始设为0，后续会更新为实际数量
    );

    %% 2. 初始化多目标PSO
    % --- 粒子群初始化 ---
    num_dims = 2; % 网格长度和旋转角
    position = zeros(swarm_size, num_dims);
    velocity = zeros(swarm_size, num_dims);

    % 多目标评价指标
    objectives = zeros(swarm_size, 2); % [功率(最大化), 成本(最小化)]

    % 个体最优记录
    p_best = zeros(swarm_size, num_dims);
    p_best_objectives = zeros(swarm_size, 2);

    % 初始化约束违反标志数组
    particle_constraint_violated = false(swarm_size, 1);
    archive_constraint_flags = [];

    % 初始化存档管理器
    objective_minmax = [1, -1]; % 目标1(功率)最大化，目标2(成本)最小化
    archive_manager = ArchiveManager(archive_max_size, objective_minmax);

    % 初始化可视化工具
    visualizer = MOPSOVisualization({'功率', '锚泊成本'}, {'MW', 'M$'});
    if show_plots
        visualizer.initializeVisualization(true);
    end

    % 历史记录
    pso_history = repmat(struct('iteration', 0, 'pareto_front', [], 'pareto_metrics', [], 'complete_analysis', []), 1, max_iterations);

    % 多目标性能指标历史
    metrics_history = cell(1, max_iterations);
    pareto_front_history = cell(1, max_iterations);

    % 初始化粒子位置
    position(:, 1) = length_min + (length_max - length_min) * rand(swarm_size, 1); % 网格长度
    position(:, 2) = rotation_min + (rotation_max - rotation_min) * rand(swarm_size, 1); % 旋转角

    % 初始化速度
    velocity(:, 1) = (rand(swarm_size, 1) - 0.5) * (length_max - length_min) * 0.1;
    velocity(:, 2) = (rand(swarm_size, 1) - 0.5) * (rotation_max - rotation_min) * 0.1;

    % 初始化个体最优
    p_best = position;

    %% 3. MOPSO主循环
    fprintf('开始MOPSO主循环...\n');
    iter_dominated_objectives = cell(1, max_iterations);

    for iter = 1:max_iterations
        fprintf('--- 第 %d 代 ---\n', iter);
        w = w_start - (w_start - w_end) * (iter / max_iterations); % 更新惯性权重

        particle_analysis_results = cell(swarm_size, 1); % 存储本代每个粒子的详细分析结果
        current_iter_objectives = zeros(swarm_size, 2); % 当前代所有粒子的目标值
        is_dominated_initial = false(swarm_size, 1); % 用于 parfor 内部标记初始支配状态
        constraint_violated_array = false(swarm_size, 1); % 用于 parfor 内部标记约束违反状态

        %% 3.1. 评估当前代粒子
        % 使用 parfor 替换 for 来并行评估粒子
        % 预初始化可能在parfor中使用的变量，避免警告
        direction_analysis_results_template = struct(...
            'wind_directions_deg', [], ...
            'wind_directions_rad', [], ...
            'num_directions', 0, ...
            'anchor_forces', {{}}, ...
            'calculation_success', [], ...
            'error_messages', {{}} ...
        );
        adapted_forces_template = struct();

        parfor i = 1:swarm_size
            % 初始化可能在条件分支中使用的变量
            direction_analysis_results = direction_analysis_results_template;
            adapted_forces = adapted_forces_template;
            % fprintf('  评估粒子 %d/%d...\n', i, swarm_size); % 注释掉 parfor 内部的频繁输出

            try
                % a. 获取当前粒子网格参数
                current_pos = position(i, :);
                grid_params = struct('length', current_pos(1), 'rotation', current_pos(2));
                % fprintf('    网格参数: L=%.2f m, R=%.2f deg\n', grid_params.length, rad2deg(grid_params.rotation)); % 注释掉

                % b. 生成布局与计算功率
                [candidatePoints, candidateTriangles, ~, grid_nodes] = Grid_Generator(grid_params, boundary);
                [validTriangles, ~] = filterTrianglesByBoundary(grid_nodes, candidateTriangles, boundary, true); % 静默过滤

                if isempty(validTriangles)
                    % fprintf('    警告: 边界内无有效三角形\n'); % 注释掉
                    % 设置非常差的目标值
                    current_iter_objectives(i, :) = [0, Inf]; % 零功率，无穷成本
                    % dominated_indices = [dominated_indices; i]; % 替换为标记
                    is_dominated_initial(i) = true;
                    particle_analysis_results{i} = struct('error', '边界内无有效三角形', 'objectives', [0, Inf]); % 记录原因
                    continue; % 跳过后续计算
                end

                candidateTriangles = validTriangles;
                % 重新计算候选点 (重心)
                newCandidatePoints = zeros(size(candidateTriangles, 1), 2);
                for tri_idx = 1:size(candidateTriangles, 1)
                    tri_vertices = grid_nodes(candidateTriangles(tri_idx, :), :);
                    newCandidatePoints(tri_idx, :) = mean(tri_vertices, 1);
                end
                candidatePoints = newCandidatePoints;

                % 添加参数传递日志 - 可以保留，因为它只在循环开始时执行一次（如果必要）
                % fprintf('准备调用WindFarmLayoutOptimizer，传递最小风机间距: %.2f m\n', min_turbine_distance); % 注释掉

                [turbine_positions, power_output, ~] = WindFarmLayoutOptimizer(...
                    candidatePoints, grid_params, boundary, min_turbine_distance, wind_params);

                if isempty(turbine_positions) || ~isfield(power_output, 'total_power') || power_output.total_power <= 0
                    % fprintf('    警告: 风机布局失败或功率为零\n'); % 注释掉
                    % 设置非常差的目标值
                    current_iter_objectives(i, :) = [0, Inf]; % 零功率，无穷成本
                    % dominated_indices = [dominated_indices; i]; % 替换为标记
                    is_dominated_initial(i) = true;
                    particle_analysis_results{i} = struct('error', '风机布局失败或功率为零', 'objectives', [0, Inf]); % 记录原因
                    continue; % 跳过后续计算
                end

                % fprintf('    布局成功: %d 风机, 总功率 %.2f MW\n', size(turbine_positions,1), power_output.total_power/1e3); % 注释掉

                % c. 获取锚点信息
                % -- 新增：构建 TurbineClassifier 的绘图选项 --
                turbinePlotFilename = sprintf('turbine_class_iter%d_particle%d.png', iter, i);
                turbinePlotSavePath = '';
                if enable_plot_saving
                    turbinePlotSavePath = fullfile(runResultsPath, turbinePlotFilename);
                end
                turbinePlotOptions = struct('display', false, 'savePath', turbinePlotSavePath);
                % -- 选项构建结束 --
                [turbine_classes] = TurbineClassifier(grid_params, wind_params.main_wind_dir, turbine_positions, grid_nodes, candidateTriangles, boundary, turbinePlotOptions); % <-- 传递选项
                [anchorMap, anchorPositions, ~] = createDedicatedAnchorMapping(turbine_positions, turbine_classes, grid_nodes, candidateTriangles, grid_params.rotation);

                if isempty(anchorMap) || isempty(anchorPositions)
                    % fprintf('    警告: 锚点映射失败\n'); % 注释掉
                    % 设置非常差的目标值
                    current_iter_objectives(i, :) = [power_output.total_power/1e3, Inf]; % 保留功率，无穷成本
                    % dominated_indices = [dominated_indices; i]; % 替换为标记
                    is_dominated_initial(i) = true;
                    particle_analysis_results{i} = struct('error', '锚点映射失败', 'objectives', current_iter_objectives(i, :)); % 记录原因
                    continue; % 跳过后续计算
                end

                % fprintf('    锚点映射成功: %d 锚点\n', size(anchorPositions, 1)); % 注释掉

                % d. 计算实际锚点角度
                actual_angles_per_turbine = calculateActualAnchorAngles(turbine_positions, anchorMap, anchorPositions);

                % e. 执行系泊优化 (获取参数和位移)
                rAnchor = 0.577 * grid_params.length;
                % fprintf('    计算锚点半径: %.2f m\n', rAnchor); % 注释掉
                [optimized_mooring_status, ~] = MooringOptimizer(grid_params, depth, rFair, zFair, rAnchor, angles_mat, target_displacement, constant_force, test_angles, num_directions, mooring_params);

                % 系泊优化结果处理
                if ~optimized_mooring_status.success
                    % fprintf('    警告: 系泊优化失败 (MooringOptimizer)，但将继续使用最接近的参数\n'); % 注释掉
                    mooring_failure_multiplier = 1.0; % 修改：不再作为惩罚系数，仅作为标识

                    % 确保必要字段存在
                    if ~isfield(optimized_mooring_status, 'chain_length') || optimized_mooring_status.chain_length <= 0
                        % fprintf('    自动设置默认锚链长度: 500 m\n'); % 注释掉
                        optimized_mooring_status.chain_length = 500;
                    end
                else
                    % fprintf('    系泊优化成功: 直径=%.1f mm, 接触长度=%.1f m, 最大位移=%.2f m\n', ...
                    %     optimized_mooring_status.chain_diameter, optimized_mooring_status.contact_length, optimized_mooring_status.max_displacement); % 注释掉
                    mooring_failure_multiplier = 1.0; % 修改：统一设为1.0
                end

                % f. 执行力分析 (获取安全系数)
                if use_multi_direction
                    % 使用多风向分析计算锚点最危险受力
                    fprintf('    使用多风向分析计算锚点最危险受力...\n');
                    [sharedAnchorForces, direction_analysis_results] = analyzeMultiDirectionAnchorForces(optimized_mooring_status, turbine_classes, turbine_positions, anchorMap, anchorPositions, constant_force, depth, rFair, zFair, rAnchor, wind_directions);
                else
                    % 使用单风向分析
                    fprintf('    使用单风向分析计算锚点受力...\n');
                    [mooring_forces_raw] = MooringForceAnalyzer(optimized_mooring_status, turbine_classes, ...
                        wind_params.main_wind_dir, constant_force, depth, rFair, zFair, rAnchor, actual_angles_per_turbine);

                    % 格式转换 - 传递 depth 参数
                    adapted_forces = convertMooringForcesFormat(mooring_forces_raw, turbine_classes, turbine_positions, anchorMap, anchorPositions, optimized_mooring_status, depth);

                    % 计算锚点合力
                    [sharedAnchorForces] = calculateIndividualAnchorForces(anchorPositions, adapted_forces, anchorMap);
                end

                if ~isfield(sharedAnchorForces, 'safetyFactors') || isempty(sharedAnchorForces.safetyFactors)
                    % fprintf('    警告: 无法计算锚点安全系数，使用默认值\n'); % 注释掉
                    min_sf = 1.0; % 使用默认安全系数
                    sharedAnchorForces.safetyFactors = ones(size(anchorPositions, 1), 1) * min_sf;
                else
                    min_sf = min(sharedAnchorForces.safetyFactors);
                end

                % fprintf('    力分析完成: 最小安全系数 = %.3f\n', min_sf); % 注释掉

                % g. 计算总锚泊成本
                [anchorCost, anchor_details] = calculate_total_anchor_cost(sharedAnchorForces, anchorPositions);
                num_turbines = size(turbine_positions, 1);

                % 直接将 num_turbines 传递给成本计算函数
                [chainCost] = calculate_total_chain_cost(optimized_mooring_status, costParams, num_turbines);

                if ~isfinite(anchorCost) || ~isfinite(chainCost) || anchorCost < 0 || chainCost < 0
                    % fprintf('    警告: 成本计算失败 (anchor=%.2f, chain=%.2f)，使用默认值\n', anchorCost, chainCost); % 注释掉
                    anchorCost = 0.5 * num_turbines; % 使用默认锚点成本（每风机0.5M$）
                    chainCost = 0.3 * num_turbines; % 使用默认锚链成本（每风机0.3M$）
                end

                total_mooring_cost = anchorCost + chainCost;
                % fprintf('    成本计算完成: 总成本 = %.3f M$\n', total_mooring_cost); % 注释掉

                % 计算锚点共享信息
                num_anchors = size(anchorPositions, 1);
                anchorSharingInfo = repmat(struct('connections', 0, 'details', []), num_anchors, 1);
                for k = 1:num_anchors
                    connected_lines_indices = find(anchorMap(:, 3) == k);
                    num_connections = length(connected_lines_indices);
                    details = repmat(struct('turbine_id', 0, 'line_id', 0, 'angle_deg', 0), num_connections, 1);
                    for j = 1:num_connections
                        map_row_idx = connected_lines_indices(j);
                        turbine_id = anchorMap(map_row_idx, 1);
                        line_id = anchorMap(map_row_idx, 2);
                        turbine_pos = turbine_positions(turbine_id, :);
                        anchor_pos = anchorPositions(k, :);
                        direction_vector = anchor_pos - turbine_pos;
                        angle_deg = atan2d(direction_vector(2), direction_vector(1));
                        if angle_deg < 0, angle_deg = angle_deg + 360; end
                        details(j) = struct('turbine_id', turbine_id, 'line_id', line_id, 'angle_deg', angle_deg);
                    end
                    anchorSharingInfo(k) = struct('connections', num_connections, 'details', details);
                end

                % 提取单个锚点实际成本
                if isfield(anchor_details, 'anchor_costs') && ~isempty(anchor_details.anchor_costs)
                    individual_anchor_costs = anchor_details.anchor_costs;
                    % 验证数量是否匹配，以防万一
                    if length(individual_anchor_costs) ~= num_anchors
                       fprintf('    警告: 从anchor_details提取的成本数量(%d)与锚点数量(%d)不符!\n', ...
                           length(individual_anchor_costs), num_anchors);
                       % 调整大小或使用空值以避免后续错误
                       if length(individual_anchor_costs) > num_anchors
                           individual_anchor_costs = individual_anchor_costs(1:num_anchors);
                       elseif length(individual_anchor_costs) < num_anchors
                           individual_anchor_costs = [individual_anchor_costs; zeros(num_anchors - length(individual_anchor_costs), 1)];
                       end
                    end
                else
                    % fprintf('    警告: anchor_details中未找到有效的anchor_costs字段，将成本设为空\n'); % 注释掉
                    individual_anchor_costs = [];
                end

                % h. 计算惩罚 (多目标优化中不直接添加到目标值，而是在比较时处理)
                sf_penalty = calculate_sf_penalty(min_sf, required_min_sf, sf_penalty_coefficient, epsilon);
                disp_penalty = calculate_disp_penalty(optimized_mooring_status.max_displacement, target_displacement, disp_penalty_coefficient);
                mooring_failure_penalty_value = calculate_mooring_failure_penalty(optimized_mooring_status.success, mooring_failure_penalty_coefficient);

                % 检查是否违反约束
                constraint_violated = false;

                % 检查安全系数约束
                if min_sf < required_min_sf || ~isfinite(min_sf)
                    constraint_violated = true;
                end

                % 检查位移约束
                if optimized_mooring_status.max_displacement > target_displacement || ~isfinite(optimized_mooring_status.max_displacement)
                    constraint_violated = true;
                end

                % 检查系泊优化成功约束
                if ~optimized_mooring_status.success
                    constraint_violated = true;
                end

                % 存储约束违反标志
                constraint_violated_array(i) = constraint_violated;

                % 修改：不直接应用系泊失败惩罚到成本目标
                penalized_cost = total_mooring_cost; % 不直接应用系泊失败惩罚到目标函数

                % i. 设置多目标值 [功率(最大化), 成本(最小化)]
                power_mw = power_output.total_power / 1e3; % 单位转换为MW
                current_iter_objectives(i, :) = [power_mw, penalized_cost];

                % 创建基本分析结果结构
                analysis_result = struct(...
                    'grid_params', grid_params, ...
                    'num_turbines', size(turbine_positions,1), ...
                    'total_power', power_output.total_power, ...
                    'farm_efficiency', power_output.farm_efficiency, ...
                    'num_anchors', num_anchors, ...
                    'turbine_positions', turbine_positions, ...
                    'power_per_turbine', power_output.turbine_powers, ...
                    'anchor_positions', anchorPositions, ...
                    'anchor_map', anchorMap, ...
                    'shared_anchor_forces', sharedAnchorForces, ...
                    'anchor_sharing_info', anchorSharingInfo, ...
                    'anchor_details', anchor_details, ...
                    'mooring_params_optimized', optimized_mooring_status, ...
                    'min_safety_factor', min_sf, ...
                    'max_displacement', optimized_mooring_status.max_displacement, ...
                    'anchor_cost', anchorCost, ...
                    'individual_anchor_costs', individual_anchor_costs, ...
                    'chain_cost', chainCost, ...
                    'total_mooring_cost', total_mooring_cost, ...
                    'sf_penalty', sf_penalty, ...
                    'disp_penalty', disp_penalty, ...
                    'mooring_failure_penalty', mooring_failure_penalty_value, ... % 修改：使用新的罚函数值
                    'mooring_failure_multiplier', mooring_failure_multiplier, ... % 新增：保留原有的乘数用于兼容性
                    'objectives', current_iter_objectives(i, :), ...
                    'error', '' ...
                );

                % 添加多风向分析结果（如果有）
                if use_multi_direction
                    analysis_result.multi_direction_analysis = struct(...
                        'enabled', true, ...
                        'wind_directions', wind_directions, ...
                        'direction_analysis_results', direction_analysis_results ...
                    );

                    % 如果使用单风向分析，不需要添加mooring_line_forces_details
                else
                    % 添加单风向分析的系泊力详情
                    analysis_result.mooring_line_forces_details = adapted_forces;
                    analysis_result.multi_direction_analysis = struct('enabled', false);
                end

                % 存储详细分析结果
                particle_analysis_results{i} = analysis_result;

                % --- 新增：尝试保存功率热图 ---
                if enable_plot_saving % 只有当 enable_plot_saving 为 true 时才保存热图
                    try
                        % 确保有有效的位置和功率数据
                        if ~isempty(turbine_positions) && isfield(power_output, 'turbine_powers') && ~isempty(power_output.turbine_powers)
                            % 调用局部函数保存热图
                            savePowerHeatmap(turbine_positions, power_output.turbine_powers, iter, i, runResultsPath, boundary);
                        end
                    catch heatmap_err
                        % 在 parfor 中，使用 fprintf 可能导致输出混乱，但错误信息比较重要
                        fprintf('警告: 迭代 %d, 粒子 %d 的功率热图生成/保存失败: %s\n', iter, i, heatmap_err.message);
                    end
                end
                % --- 热图保存结束 ---

            catch err
                fprintf('\n==== 粒子 %d 评估错误详情 ====\n', i);
                fprintf('错误信息: %s\n', err.message);
                fprintf('错误位置: %s (行: %d)\n', err.stack(1).name, err.stack(1).line);
                fprintf('错误堆栈:\n');
                for stack_idx = 1:min(length(err.stack), 3) % 最多显示3层堆栈
                    fprintf('  [%d] %s (行: %d)\n', stack_idx, err.stack(stack_idx).name, err.stack(stack_idx).line);
                end
                fprintf('==== 错误详情结束 ====\n\n');

                % 设置最差的目标值
                current_iter_objectives(i, :) = [0, Inf]; % 零功率，无穷成本
                % dominated_indices = [dominated_indices; i]; % 替换为标记
                is_dominated_initial(i) = true; % 标记为初始支配

                % 记录错误
                particle_analysis_results{i} = struct('error', err.message, 'stack', err.stack, 'objectives', [0, Inf]);
            end
        end % 结束 parfor i = 1:swarm_size

        % 在 parfor 循环结束后重建 dominated_indices
        dominated_indices = find(is_dominated_initial);

        % 将约束违反标志从parfor循环中复制出来
        particle_constraint_violated = constraint_violated_array;
        fprintf('  迭代 %d: 共 %d 个粒子违反约束。\n', iter, sum(particle_constraint_violated));

        % 额外检查 particle_analysis_results 中记录的 error 字段
        for k_idx = 1:swarm_size
           % 检查索引是否有效、结果是否为结构体、是否存在非空error字段
           if ~is_dominated_initial(k_idx) && ... % 如果不是初始支配
              iscell(particle_analysis_results) && numel(particle_analysis_results) >= k_idx && ...
              ~isempty(particle_analysis_results{k_idx}) && ... % 确保单元格不为空
              isstruct(particle_analysis_results{k_idx}) && ...
              isfield(particle_analysis_results{k_idx}, 'error') && ~isempty(particle_analysis_results{k_idx}.error)

               % fprintf('  注意: 粒子 %d 在 parfor 后因错误被标记为支配。\n', k_idx); % 可选的调试信息
               dominated_indices = [dominated_indices; k_idx]; % 添加到支配列表
           end
        end
        dominated_indices = unique(dominated_indices); % 确保索引唯一
        fprintf('  迭代 %d: 共 %d 个粒子被识别为支配 (初始+错误)。\n', iter, length(dominated_indices));

        % 存储当前代所有粒子的详细分析结果
        pso_history(iter).complete_analysis = particle_analysis_results;
        fprintf('  迭代 %d: 已存储所有 %d 个粒子的详细分析结果。\n', iter, swarm_size);

        %% 3.2. 更新个体最优 (p_best)
        for i = 1:swarm_size
            % 跳过评估失败或被支配的粒子
            % if isempty(particle_analysis_results{i}) || (isfield(particle_analysis_results{i}, 'error') && ~isempty(particle_analysis_results{i}.error)) || ismember(i, dominated_indices)
            % 检查 is_dominated_initial 状态 和 analysis_results 是否有效
            if is_dominated_initial(i) || isempty(particle_analysis_results{i}) || (isstruct(particle_analysis_results{i}) && isfield(particle_analysis_results{i}, 'error') && ~isempty(particle_analysis_results{i}.error))
                continue;
            end

            % 初始化p_best_objectives (如果尚未初始化)
            if all(p_best_objectives(i, :) == 0)
                p_best_objectives(i, :) = current_iter_objectives(i, :);
                p_best(i, :) = position(i, :);
                continue;
            end

            % 判断当前位置是否支配p_best
            current_dominates = IsDominatesB(current_iter_objectives(i, :), p_best_objectives(i, :), objective_minmax);
            pbest_dominates = IsDominatesB(p_best_objectives(i, :), current_iter_objectives(i, :), objective_minmax);

            if current_dominates || (~pbest_dominates && rand < 0.5)
                % 如果当前位置支配p_best，或者两者互不支配且随机选择当前位置
                p_best_objectives(i, :) = current_iter_objectives(i, :);
                p_best(i, :) = position(i, :);
            end
        end

        %% 3.3. 更新存档
        [added_indices, dominated_by_archive, archive_constraint_flags] = updateArchive(archive_manager, position, current_iter_objectives, particle_constraint_violated, particle_analysis_results);
        dominated_indices = unique([dominated_indices; dominated_by_archive']);

        % 获取存档内容
        [archive_positions, archive_objectives] = archive_manager.getArchiveContents();
        fprintf('  当前存档大小: %d 个非支配解\n', size(archive_positions, 1));

        % 调试：检查添加到存档的粒子是否有error
        fprintf('\n==== 存档更新分析 ====\n');
        fprintf('新添加到存档的粒子索引: ');
        if isempty(added_indices)
            fprintf('无\n');
        else
            fprintf('%s\n', mat2str(added_indices));
            % 检查这些添加的粒子是否有错误
            for ai = 1:length(added_indices)
                idx = added_indices(ai);
                if idx <= numel(particle_analysis_results) && isstruct(particle_analysis_results{idx}) && isfield(particle_analysis_results{idx}, 'error')
                    fprintf('  警告: 添加到存档的粒子 %d 包含error字段: %s\n', idx, particle_analysis_results{idx}.error);
                end
            end
        end

        % 打印存档中所有解的目标值和对应位置
        fprintf('存档中所有解 (索引 | 位置 | 目标值):\n');
        for ai = 1:size(archive_objectives, 1)
            fprintf('  [%d] 位置=[%.2f, %.2f], 目标值=[%.4f, %.4f]\n', ai, ...
                archive_positions(ai, 1), archive_positions(ai, 2), ...
                archive_objectives(ai, 1), archive_objectives(ai, 2));

            % 查找此存档解对应的原始粒子索引
            matched = false;
            for pi = 1:size(current_iter_objectives, 1)
                if all(abs(current_iter_objectives(pi, :) - archive_objectives(ai, :)) < 1e-6)
                    matched = true;
                    fprintf('    匹配到当前代粒子索引: %d\n', pi);
                    if pi <= numel(particle_analysis_results) && isstruct(particle_analysis_results{pi}) && ~isempty(particle_analysis_results{pi}.error)
                        fprintf('    警告: 此粒子包含真实错误: %s\n', particle_analysis_results{pi}.error);
                    end
                    break;
                end
            end
            if ~matched
                fprintf('    未匹配到当前代粒子 (可能来自之前的迭代)\n');
            end
        end
        fprintf('==== 存档更新分析结束 ====\n\n');

        % 存储被支配的目标值，用于可视化
        if ~isempty(dominated_indices)
            iter_dominated_objectives{iter} = current_iter_objectives(dominated_indices, :);
        else
            iter_dominated_objectives{iter} = [];
        end

        %% 3.4. 更新粒子位置和速度
        for i = 1:swarm_size
            % 选择一个全局最优引导者（只从不违反约束的解中选择）
            leader_idx = selectLeader(archive_positions, archive_objectives, archive_constraint_flags, leader_selection_method);

            if isempty(leader_idx)
                % 如果存档为空或没有不违反约束的解，使用当前粒子的个体最优
                g_best_position = p_best(i, :);
            else
                % 使用选中的存档引导者
                g_best_position = archive_positions(leader_idx, :);
            end

            % 更新速度
            r1 = rand(1, num_dims);
            r2 = rand(1, num_dims);

            velocity(i, :) = w * velocity(i, :) ...
                            + c1 * r1 .* (p_best(i, :) - position(i, :)) ...
                            + c2 * r2 .* (g_best_position - position(i, :));

            % 更新位置
            position(i, :) = position(i, :) + velocity(i, :);

            % 边界处理
            position(i, 1) = max(min(position(i, 1), length_max), length_min);
            position(i, 2) = max(min(position(i, 2), rotation_max), rotation_min);
        end

        %% 3.5. 计算性能指标和存储历史
        if ~isempty(archive_objectives)
            metrics = archive_manager.calculateParetoMetrics();
            fprintf('  Pareto前沿指标: 超体积=%.4f, 扩展度=%.4f, 均匀度=%.4f\n', ...
                metrics.hypervolume, metrics.spread, metrics.spacing);
        else
            metrics = struct('archive_size', 0, 'hypervolume', 0, 'spread', NaN, 'spacing', NaN);
        end

        % 存储历史
        metrics_history{iter} = metrics;
        pareto_front_history{iter} = archive_objectives;

        % 更新历史记录
        pso_history(iter).iteration = iter;
        pso_history(iter).pareto_front = archive_objectives;
        pso_history(iter).pareto_metrics = metrics;

        %% 3.6. 更新可视化
        if show_plots
            % 更新Pareto前沿图
            visualizer.updateParetoFront(archive_objectives, current_iter_objectives, ...
                iter_dominated_objectives{iter}, iter, archive_constraint_flags);

            % 如果已经完成了至少2次迭代，更新指标图
            if iter >= 2
                valid_iterations = 1:iter;
                visualizer.updateMetrics(valid_iterations, metrics_history(valid_iterations));

                % 更新历史图
                visualizer.updateHistory(valid_iterations, pareto_front_history(valid_iterations));
            end
        end

        fprintf('--- 第 %d 代完成, 存档大小: %d ---\n\n', iter, size(archive_objectives, 1));

        % 迭代结束前进行存档与粒子分析结果的完整性检查
        fprintf('\n==== 迭代 %d 存档完整性检查 ====\n', iter);
        [archive_positions, archive_objectives] = archive_manager.getArchiveContents();
        for ai = 1:size(archive_objectives, 1)
            fprintf('存档解 %d: 位置=[%.4f, %.2f], 目标值=[%.4f, %.4f]\n', ai, ...
                archive_positions(ai, 1), archive_positions(ai, 2), ...
                archive_objectives(ai, 1), archive_objectives(ai, 2));

            % 检查此存档解是否来自当前迭代
            found_in_current = false;
            for pi = 1:size(current_iter_objectives, 1)
                if all(abs(current_iter_objectives(pi, :) - archive_objectives(ai, :)) < 1e-6)
                    found_in_current = true;
                    fprintf('  对应当前代粒子 %d\n', pi);

                    % 检查此粒子是否有错误
                    if pi <= numel(particle_analysis_results) && ...
                       isstruct(particle_analysis_results{pi}) && ...
                       ~isempty(particle_analysis_results{pi}.error)
                        fprintf('  警告: 此粒子包含真实错误! %s\n', particle_analysis_results{pi}.error);
                    end
                    break;
                end
            end

            if ~found_in_current
                fprintf('  未在当前代找到对应粒子，来自之前迭代\n');
            end
        end
        fprintf('==== 完整性检查结束 ====\n\n');
    end

    %% 4. 选择最终解并返回结果
    % 如果存档不为空，选择功率最高的解作为最终结果（只从不违反约束的解中选择）
    if archive_manager.getArchiveSize() > 0
        [archive_positions, archive_objectives] = archive_manager.getArchiveContents();

        % 只考虑不违反约束的解
        valid_indices = find(~archive_constraint_flags);

        if ~isempty(valid_indices)
            % 从不违反约束的解中选择功率最高的解
            valid_objectives = archive_objectives(valid_indices, :);
            [~, max_power_idx] = max(valid_objectives(:, 1));
            final_position = archive_positions(valid_indices(max_power_idx), :);

            best_grid_params = struct('length', final_position(1), 'rotation', final_position(2));
            fprintf('\n优化完成! 最终选择的解: 长度 = %.2f m, 旋转角 = %.2f rad (%.2f°)\n', ...
                best_grid_params.length, best_grid_params.rotation, rad2deg(best_grid_params.rotation));
            fprintf('功率 = %.2f MW, 锚泊成本 = %.2f M$\n', ...
                valid_objectives(max_power_idx, 1), valid_objectives(max_power_idx, 2));
            fprintf('注意: 最终选择的解满足所有约束条件\n');
        else
            % 如果没有不违反约束的解，使用默认值（中间值）
            best_grid_params = struct('length', (length_min + length_max)/2, 'rotation', (rotation_min + rotation_max)/2);
            fprintf('\n警告: 优化未找到满足约束的有效解，使用默认参数: 长度 = %.2f m, 旋转角 = %.2f rad (%.2f°)\n', ...
                best_grid_params.length, best_grid_params.rotation, rad2deg(best_grid_params.rotation));
        end
    else
        % 如果存档为空，使用默认值（中间值）
        best_grid_params = struct('length', (length_min + length_max)/2, 'rotation', (rotation_min + rotation_max)/2);
        fprintf('\n警告: 优化未找到有效解，使用默认参数: 长度 = %.2f m, 旋转角 = %.2f rad (%.2f°)\n', ...
            best_grid_params.length, best_grid_params.rotation, rad2deg(best_grid_params.rotation));
    end

    % 保存所有Pareto前沿到最后一个历史记录
    pso_history(end).all_pareto_fronts = pareto_front_history;
    pso_history(end).all_metrics = metrics_history;

    % 清理图形资源（根据需要调整）
    if show_plots
        fprintf('图形将保持显示，方便查看优化结果。请手动关闭窗口。\n');
    end
end

%% 辅助函数：判断A是否支配B
function dominates = IsDominatesB(A, B, minmax)
    % 判断解A是否支配解B
    % A, B: 目标值向量
    % minmax: 目标方向，1表示最大化，-1表示最小化

    if nargin < 3
        minmax = [1, -1]; % 默认目标1最大化，目标2最小化
    end

    better_in_any = false;
    worse_in_any = false;

    for i = 1:length(A)
        if minmax(i) == 1
            % 最大化目标
            if A(i) > B(i)
                better_in_any = true;
            elseif A(i) < B(i)
                worse_in_any = true;
            end
        else
            % 最小化目标
            if A(i) < B(i)
                better_in_any = true;
            elseif A(i) > B(i)
                worse_in_any = true;
            end
        end
    end

    dominates = better_in_any && ~worse_in_any;
end

%% 以下函数用于保持与PSO_GridOptimizer接口一致
function mooringForces = convertMooringForcesFormat(mooring_forces_raw, turbine_classes, turbine_positions, anchorMap, anchorPositions, mooring_status, depth)
    % 这是一个接口转换函数，调用原始PSO优化器中的函数
    % 对MOPSO来说这是一个黑盒函数，只需要确保调用正确

    % 不需要再检查depth是否存在，因为它现在是一个函数参数
    % 但仍然保留基本的有效性检查和备用机制
    if ~isfinite(depth) || depth <= 0  % 只检查depth是否有效
        % 如果传入的depth无效，尝试从mooring_status获取
        if isfield(mooring_status, 'depth') && isfinite(mooring_status.depth) && mooring_status.depth > 0
            depth_local = mooring_status.depth;
            fprintf('警告: 传入的depth参数无效，从mooring_status获取depth=%.2f m\n', depth_local);
        else
            % 所有方法都失败时使用默认值
            depth_local = 100;  % 默认值
            warning('警告: 传入的depth参数无效，且mooring_status中也未找到有效值，使用默认水深%.2f m', depth_local);
        end
    else
        depth_local = depth;  % 使用传入的有效depth值
        % fprintf('使用传入的水深: %.2f m\n', depth_local);  % 可选的调试信息
    end

    % 在MOPSO内部实现convertTraditionalForcesToMappedFormat函数
    mooringForces = convertTraditionalForcesToMappedFormat_internal(mooring_forces_raw, turbine_positions, turbine_classes, anchorMap, anchorPositions, mooring_status, depth_local); % 传递 depth_local
end

% 添加从PSO_GridOptimizer.m复制的函数 - 重命名为内部版本
% 修改签名以接受 depth 参数
%% 自定义存档更新函数，确保违反约束的解不会被添加到Pareto前沿
function [added_indices, dominated_by_archive, constraint_flags] = updateArchive(archive_manager, positions, objectives, constraint_violated, particle_analysis_results)
    % updateArchive - 更新非支配解存档，排除违反约束的解
    %
    % 输入:
    %   archive_manager - 存档管理器对象
    %   positions - 候选解的位置
    %   objectives - 候选解的目标值
    %   constraint_violated - 约束违反标志
    %   particle_analysis_results - 粒子分析结果，用于检查error字段(可选)
    %
    % 输出:
    %   added_indices - 新添加到存档的解的索引
    %   dominated_by_archive - 被存档中解支配的解的索引
    %   constraint_flags - 更新后的约束违反标志

    % 获取当前存档内容
    [archive_positions, archive_objectives] = archive_manager.getArchiveContents();

    % 获取当前存档大小
    archive_size = size(archive_positions, 1);

    % 初始化约束违反标志
    if archive_size > 0
        constraint_flags = false(archive_size, 1);
    else
        constraint_flags = [];
    end

    % 初始化返回值
    added_indices = [];
    dominated_by_archive = [];

    % 过滤掉违反约束的解
    valid_indices = ~constraint_violated;

    % 如果所有解都违反约束，直接返回
    if ~any(valid_indices)
        fprintf('  警告: 所有解都违反约束，不更新存档\n');
        return;
    end

    % 只保留不违反约束的解
    valid_positions = positions(valid_indices, :);
    valid_objectives = objectives(valid_indices, :);

    % 获取有效解的原始索引
    valid_original_indices = find(valid_indices);

    % 使用存档管理器的updateArchive方法更新存档
    if nargin >= 5
        [added_valid_indices, dominated_valid_indices] = archive_manager.updateArchive(valid_positions, valid_objectives, particle_analysis_results);
    else
        [added_valid_indices, dominated_valid_indices] = archive_manager.updateArchive(valid_positions, valid_objectives);
    end

    % 将有效解的索引转换回原始索引
    if ~isempty(added_valid_indices)
        added_indices = valid_original_indices(added_valid_indices);
    end

    if ~isempty(dominated_valid_indices)
        dominated_by_archive = valid_original_indices(dominated_valid_indices);
    end

    % 获取更新后的存档内容
    [archive_positions, archive_objectives] = archive_manager.getArchiveContents();

    % 更新约束违反标志
    constraint_flags = false(size(archive_positions, 1), 1);
end

%% 自定义引导者选择函数，确保只从不违反约束的解中选择引导者
function leader_idx = selectLeader(archive_positions, archive_objectives, archive_constraint_flags, selection_method)
    % selectLeader - 从存档中选择引导者，只考虑不违反约束的解
    %
    % 输入:
    %   archive_positions - 存档中的位置
    %   archive_objectives - 存档中的目标值
    %   archive_constraint_flags - 存档中解的约束违反标志
    %   selection_method - 选择方法，'random', 'crowding', 'binary'
    %
    % 输出:
    %   leader_idx - 选择的引导者索引

    % 获取存档大小
    archive_size = size(archive_positions, 1);

    % 如果存档为空，返回空
    if archive_size == 0
        leader_idx = [];
        return;
    end

    % 只考虑不违反约束的解
    valid_indices = find(~archive_constraint_flags);

    % 如果没有不违反约束的解，返回空
    if isempty(valid_indices)
        leader_idx = [];
        return;
    end

    % 如果只有一个不违反约束的解，直接返回
    if length(valid_indices) == 1
        leader_idx = valid_indices(1);
        return;
    end

    % 获取有效解的目标值
    valid_objectives = archive_objectives(valid_indices, :);

    % 根据选择方法选择引导者
    switch selection_method
        case 'random'
            % 随机选择
            random_idx = randi(length(valid_indices));
            leader_idx = valid_indices(random_idx);

        case 'crowding'
            % 基于拥挤距离选择
            crowding_distances = calculateCrowdingDistance(valid_objectives);

            % 基于拥挤距离的轮盘赌选择
            probs = crowding_distances / sum(crowding_distances);
            cumprobs = cumsum(probs);
            r = rand();
            selected_idx = find(cumprobs >= r, 1, 'first');

            % 如果没有找到（可能是由于数值误差），选择最后一个
            if isempty(selected_idx)
                selected_idx = length(valid_indices);
            end

            leader_idx = valid_indices(selected_idx);

        case 'binary'
            % 二元锦标赛选择
            idx1 = randi(length(valid_indices));
            idx2 = randi(length(valid_indices));

            % 选择拥挤距离更大的
            crowding_distances = calculateCrowdingDistance(valid_objectives);
            if crowding_distances(idx1) >= crowding_distances(idx2)
                leader_idx = valid_indices(idx1);
            else
                leader_idx = valid_indices(idx2);
            end

        otherwise
            % 默认使用拥挤距离选择
            crowding_distances = calculateCrowdingDistance(valid_objectives);
            [~, max_idx] = max(crowding_distances);
            leader_idx = valid_indices(max_idx);
    end
end

%% 计算拥挤距离
function crowding_distance = calculateCrowdingDistance(objectives)
    % calculateCrowdingDistance - 计算解集的拥挤距离
    %
    % 输入:
    %   objectives - 目标函数值矩阵，每行表示一个解的多个目标值
    %
    % 输出:
    %   crowding_distance - 每个解的拥挤距离

    n = size(objectives, 1);  % 解的数量
    m = size(objectives, 2);  % 目标的数量

    if n <= 2
        % 如果只有1-2个解，赋予最大拥挤距离
        crowding_distance = Inf * ones(n, 1);
        return;
    end

    % 初始化拥挤距离
    crowding_distance = zeros(n, 1);

    % 对每个目标计算拥挤距离
    for i = 1:m
        % 按当前目标排序
        [sorted_obj, sorted_indices] = sort(objectives(:,i));

        % 端点解的拥挤距离设为无穷大
        crowding_distance(sorted_indices(1)) = Inf;
        crowding_distance(sorted_indices(n)) = Inf;

        % 计算其他解的拥挤距离
        for j = 2:(n-1)
            % 计算归一化的距离贡献
            obj_range = sorted_obj(n) - sorted_obj(1);
            if obj_range > 0
                distance_contribution = (sorted_obj(j+1) - sorted_obj(j-1)) / obj_range;
                crowding_distance(sorted_indices(j)) = crowding_distance(sorted_indices(j)) + distance_contribution;
            end
        end
    end
end

function mooringForces = convertTraditionalForcesToMappedFormat_internal(mooring_forces_traditional, turbine_positions, turbine_classes, anchorMap, anchorPositions, mooring_status, depth)
    % convertTraditionalForcesToMappedFormat - 将传统系泊力结构转换为映射格式
    % (内部版本，接收 depth 作为参数)
    %
    % 输入:
    %   mooring_forces_traditional - 传统系泊力计算结果
    %   turbine_positions - 风机位置 [x, y]
    %   turbine_classes - 风机类型分类结构
    %   anchorMap - 锚点映射关系
    %   anchorPositions - 锚点位置
    %   mooring_status - 系泊优化状态结构，包含锚链长度信息（可选）
    %   depth - 水深 (显式传递)
    %
    % 输出:
    %   mooringForces - 映射格式的系泊力数据结构
    fprintf('开始转换系泊力数据结构 (使用 depth=%.2f m)...\n', depth); % 使用传入的 depth

    % 获取风机数量
    numTurbines = size(turbine_positions, 1);
    fprintf('  处理%d个风机的系泊力数据\n', numTurbines);

    % 创建映射格式的系泊力结构体
    mooringForces = struct();
    mooringForces.forces = cell(numTurbines, 1);
    mooringForces.forces_xyz = cell(numTurbines, 1);
    mooringForces.magnitudes = cell(numTurbines, 1);
    mooringForces.angles = cell(numTurbines, 1);
    mooringForces.resultantForce = zeros(numTurbines, 1);
    mooringForces.safety_factors = cell(numTurbines, 1);
    mooringForces.turbineTypes = zeros(numTurbines, 1);
    mooringForces.anchor_mapping = cell(numTurbines, 1);
    mooringForces.anchor_id_forces = cell(numTurbines, 1);

    % 复制mooring_params字段（如果存在）
    if isfield(mooring_forces_traditional, 'mooring_params')
        mooringForces.mooring_params = mooring_forces_traditional.mooring_params;
        if isfield(mooringForces.mooring_params, 'chain_diameter')
            fprintf('  复制mooring_params字段: 包含chain_diameter=%.2f mm\n', mooringForces.mooring_params.chain_diameter);
        else
            fprintf('  复制mooring_params字段，但不包含chain_diameter参数\n');
        end

        % 确保depth字段存在
        if ~isfield(mooringForces.mooring_params, 'depth')
            fprintf('  复制的mooring_params中不包含depth字段，添加depth=%.2f m\n', depth);
            mooringForces.mooring_params.depth = depth; % 使用传入的 depth
        end
    else
        fprintf('  警告: mooring_forces_traditional中不包含mooring_params字段\n');

        % 创建基本的mooring_params结构体，包含水深参数
        mooringForces.mooring_params = struct(...
            'chain_diameter', 120, ...  % 默认锚链直径120mm
            'contact_length', 150, ...  % 默认海床接触长度150m
            'main_wind_dir', 0, ...     % 默认风向0度
            'depth', depth ...            % 设置为传入的 depth
        );
        fprintf('  创建了默认的mooring_params字段，设置水深depth=%.2f m\n', depth);
    end

    % 为每个风机填充系泊力数据
    for i = 1:numTurbines
        % 确定风机类型
        if isfield(turbine_classes, 'type1_indices') && isfield(turbine_classes, 'type2_indices')
            % 使用索引列表确定类型
            if ismember(i, turbine_classes.type1_indices)
                turbine_type = 1;
                force_data = mooring_forces_traditional.type1;
            elseif ismember(i, turbine_classes.type2_indices)
                turbine_type = 2;
                force_data = mooring_forces_traditional.type2;
            else
                % 如果都不属于，默认为类型1
                fprintf('  警告: 风机%d不在已知类型列表中，默认为类型1\n', i);
                turbine_type = 1;
                force_data = mooring_forces_traditional.type1;
            end
        elseif isfield(turbine_classes, 'type') && length(turbine_classes) >= i
            % 使用直接类型字段
            turbine_type = turbine_classes(i).type;
            if turbine_type == 1
                force_data = mooring_forces_traditional.type1;
            else
                force_data = mooring_forces_traditional.type2;
            end
        else
            % 如果都不符合，默认为类型1
            fprintf('  警告: 无法确定风机%d类型，默认为类型1\n', i);
            turbine_type = 1;
            force_data = mooring_forces_traditional.type1;
        end

        % 存储风机类型
        mooringForces.turbineTypes(i) = turbine_type;

        % 创建锚点ID到全局索引的映射
        anchor_id_to_global = containers.Map('KeyType', 'double', 'ValueType', 'double');
        mooringForces.anchor_mapping{i} = anchor_id_to_global;

        % 初始化锚点力数据映射
        mooringForces.anchor_id_forces{i} = containers.Map('KeyType', 'double', 'ValueType', 'any');

        % 获取该风机的锚点映射
        turbine_anchor_indices = find(anchorMap(:, 1) == i);

        % 计算实际锚点角度
        if ~isempty(turbine_anchor_indices)
            % 初始化力和角度数组
            forces = zeros(3, 2);         % [x, y]分量
            forces_xyz = zeros(3, 3);     % [x, y, z]分量
            magnitudes = zeros(3, 1);     % 力大小
            angles = zeros(3, 1);         % 锚点角度
            safety_factors = zeros(3, 1); % 安全系数

            % 遍历该风机的所有锚点
            for j = 1:length(turbine_anchor_indices)
                anchor_map_idx = turbine_anchor_indices(j);
                local_anchor_id = anchorMap(anchor_map_idx, 2);  % 锚点ID（对应标准角度索引）
                global_anchor_idx = anchorMap(anchor_map_idx, 3); % 全局锚点索引

                % 获取实际角度（如果anchorMap结构包含第5列角度信息）
                if size(anchorMap, 2) >= 5
                    actual_angle = anchorMap(anchor_map_idx, 5);
                else
                    % 计算风机到锚点的角度
                    turbine_pos = turbine_positions(i, :);
                    anchor_pos = anchorPositions(global_anchor_idx, :);
                    direction_vector = anchor_pos - turbine_pos;
                    actual_angle = atan2d(direction_vector(2), direction_vector(1));
                    if actual_angle < 0
                        actual_angle = actual_angle + 360;
                    end
                end

                % 存储锚点ID到全局索引的映射
                anchor_id_to_global(local_anchor_id) = global_anchor_idx;

                % 存储角度
                angles(local_anchor_id) = actual_angle;

                % 从传统系泊力结构中获取力数据
                switch local_anchor_id
                    case 1
                        line_data = force_data.line1;
                    case 2
                        line_data = force_data.line2;
                    case 3
                        line_data = force_data.line3;
                    otherwise
                        fprintf('  警告: 未知锚点ID %d，使用第1根系泊线数据\n', local_anchor_id);
                        line_data = force_data.line1;
                end

                % 计算力的方向和分量
                tension = line_data.tension;
                safety_factor = line_data.safety_factor;

                % 计算力的x,y分量 - 使用实际锚点角度而不是标准角度
                force_angle_rad = deg2rad(actual_angle);
                force_x = tension * cos(force_angle_rad);
                force_y = tension * sin(force_angle_rad);

                % 存储力数据
                forces(local_anchor_id, :) = [force_x, force_y];
                forces_xyz(local_anchor_id, :) = [force_x, force_y, 0]; % z分量为0
                magnitudes(local_anchor_id) = tension;
                safety_factors(local_anchor_id) = safety_factor;

                % 创建单个锚点力数据结构
                anchor_force = struct(...
                    'force', [force_x, force_y], ...
                    'force_xyz', [force_x, force_y, 0], ...
                    'magnitude', tension, ...
                    'angle', actual_angle, ...
                    'safety_factor', safety_factor);

                % 存储到映射中
                mooringForces.anchor_id_forces{i}(local_anchor_id) = anchor_force;
            end

            % 存储该风机的力数据
            mooringForces.forces{i} = forces;
            mooringForces.forces_xyz{i} = forces_xyz;
            mooringForces.magnitudes{i} = magnitudes;
            mooringForces.angles{i} = angles;
            mooringForces.safety_factors{i} = safety_factors;

            % 计算合力大小
            resultant_force_vec = sum(forces, 1);
            mooringForces.resultantForce(i) = norm(resultant_force_vec);

            fprintf('  风机%d完成数据转换，类型=%d，锚点角度=[%.1f°, %.1f°, %.1f°]\n', ...
                i, turbine_type, angles(1), angles(2), angles(3));
        else
            fprintf('  警告: 风机%d没有锚点映射数据\n', i);

            % 使用默认值
            mooringForces.forces{i} = zeros(3, 2);
            mooringForces.forces_xyz{i} = zeros(3, 3);
            mooringForces.magnitudes{i} = zeros(3, 1);
            mooringForces.angles{i} = [0; 120; 240]; % 默认角度
            mooringForces.safety_factors{i} = ones(3, 1) * 1.5; % 默认安全系数
            mooringForces.resultantForce(i) = 0;

            % 添加默认锚点力数据
            for j = 1:3
                anchor_force = struct('force', [0, 0], ...
                                    'force_xyz', [0, 0, 0], ...
                                    'magnitude', 0, ...
                                    'angle', (j-1)*120, ...
                                    'safety_factor', 1.5);
                mooringForces.anchor_id_forces{i}(j) = anchor_force;
            end
        end
    end

    % 验证mooring_params字段是否存在
    if isfield(mooringForces, 'mooring_params')
        fprintf('  验证: mooringForces结构包含mooring_params字段\n');
        if isfield(mooringForces.mooring_params, 'chain_diameter')
            fprintf('    - chain_diameter: %.2f mm\n', mooringForces.mooring_params.chain_diameter);
        end
        if isfield(mooringForces.mooring_params, 'depth')
            fprintf('    - depth: %.2f m\n', mooringForces.mooring_params.depth);
        else
            fprintf('    警告: 缺少depth字段，正在添加...\n');
            mooringForces.mooring_params.depth = depth; % 添加传入的 depth
            fprintf('    - 已添加depth: %.2f m\n', depth);
        end
        if isfield(mooringForces.mooring_params, 'contact_length')
            fprintf('    - contact_length: %.2f m\n', mooringForces.mooring_params.contact_length);
        end
    else
        fprintf('  警告: 复制失败，mooringForces结构不包含mooring_params字段\n');
        % 添加一个最后的保障机制
        mooringForces.mooring_params = struct('depth', depth, 'chain_diameter', 120, 'contact_length', 150); % 使用传入的 depth
        fprintf('  已创建基本的mooring_params字段，设置水深depth=%.2f m\n', depth);
    end

    % 添加锚链长度和数量信息（如果不存在）
    if ~isfield(mooringForces.mooring_params, 'type1') && ~isfield(mooringForces.mooring_params, 'length')
        % 计算每种类型风机的数量
        type1_count = sum(mooringForces.turbineTypes == 1) * 3; % 每个风机3根锚链
        type2_count = sum(mooringForces.turbineTypes == 2) * 3; % 每个风机3根锚链

        % 必须使用系泊优化状态中的锚链长度
        if exist('mooring_status', 'var') && ~isempty(mooring_status) && isfield(mooring_status, 'chain_length') && mooring_status.chain_length > 0
            estimated_length = mooring_status.chain_length;
            fprintf('  从系泊优化结果获取锚链长度: %.2f m\n', estimated_length);
        else
            % 如果无法获取锚链长度，改用更健壮的错误处理
            fprintf('  警告: 系泊优化结果中未包含chain_length字段，使用默认值500m\n');
            estimated_length = 500; % 使用默认值而非抛出错误
        end

        fprintf('  添加锚链长度和数量信息...\n');
        fprintf('    - 锚链长度: %.2f m\n', estimated_length);
        fprintf('    - 类型1风机锚链数量: %d\n', type1_count);
        fprintf('    - 类型2风机锚链数量: %d\n', type2_count);

        if type1_count > 0 || type2_count > 0
            % 如果有两种类型
            mooringForces.mooring_params.type1 = struct('length', estimated_length, 'count', type1_count);
            mooringForces.mooring_params.type2 = struct('length', estimated_length, 'count', type2_count);

            % 同时添加chain_length字段，方便直接获取锚链长度
            mooringForces.mooring_params.chain_length = estimated_length;
        else
            % 如果无法确定类型，使用总数
            total_count = numTurbines * 3; % 每个风机3根锚链
            mooringForces.mooring_params.length = estimated_length;
            mooringForces.mooring_params.count = total_count;
            mooringForces.mooring_params.chain_length = estimated_length;
        end
    end

    fprintf('系泊力数据转换完成，共处理%d个风机\n', numTurbines);
end

%% 计算系泊安全系数惩罚
function sf_penalty = calculate_sf_penalty(min_sf, required_min_sf, penalty_coefficient, epsilon)
    % 与原PSO优化器相同的函数
    if ~isfinite(min_sf)
        min_sf = 0;
    end

    if min_sf < required_min_sf
        deficit_ratio = (required_min_sf / max(min_sf, epsilon)) - 1;
        sf_penalty = penalty_coefficient * deficit_ratio^2;
    else
        sf_penalty = 0;
    end
end

%% 计算位移惩罚
function disp_penalty = calculate_disp_penalty(max_disp, target_displacement, penalty_coefficient)
    % 与原PSO优化器相同的函数
    if ~isfinite(max_disp) || ~isfinite(target_displacement) || target_displacement <= 0
        disp_penalty = penalty_coefficient;
        return;
    end

    if max_disp > target_displacement
        excess_ratio = (max_disp / target_displacement) - 1;
        disp_penalty = penalty_coefficient * excess_ratio^2;
    else
        disp_penalty = 0;
    end
end

%% 计算系泊优化失败惩罚
function mooring_failure_penalty = calculate_mooring_failure_penalty(mooring_success, penalty_coefficient)
    % 计算系泊优化失败惩罚
    % 输入:
    %   mooring_success - 系泊优化是否成功 (boolean)
    %   penalty_coefficient - 惩罚系数
    % 输出:
    %   mooring_failure_penalty - 系泊优化失败惩罚值

    if ~mooring_success
        mooring_failure_penalty = penalty_coefficient; % 固定惩罚值
    else
        mooring_failure_penalty = 0;
    end
end

%% 计算实际锚点角度
function actual_angles_per_turbine = calculateActualAnchorAngles(turbine_positions, anchorMap, anchorPositions)
    % 与原PSO优化器相同的函数
    numTurbines = size(turbine_positions, 1);
    actual_angles_per_turbine = cell(numTurbines, 1);

    for i = 1:numTurbines
        turbine_pos = turbine_positions(i,:);
        turbine_anchor_indices = find(anchorMap(:, 1) == i);
        angles = zeros(3, 1);

        if length(turbine_anchor_indices) == 3
            for j=1:3
                anchor_map_idx = turbine_anchor_indices(j);
                local_anchor_id = anchorMap(anchor_map_idx, 2);
                global_anchor_idx = anchorMap(anchor_map_idx, 3);

                if global_anchor_idx > 0 && global_anchor_idx <= size(anchorPositions, 1)
                    anchor_pos = anchorPositions(global_anchor_idx, :);
                    direction_vector = anchor_pos - turbine_pos;
                    angle_deg = atan2d(direction_vector(2), direction_vector(1));
                    if angle_deg < 0, angle_deg = angle_deg + 360; end
                    angles(local_anchor_id) = deg2rad(angle_deg);
                else
                    angles(local_anchor_id) = deg2rad((local_anchor_id-1)*120 + 60);
                end
            end
        else
            angles = deg2rad([60; 180; 300]);
        end

        actual_angles_per_turbine{i} = angles;
    end
end

function str = conditional_str(condition, true_str, false_str)
    % 条件字符串生成函数
    if condition
        str = true_str;
    else
        str = false_str;
    end
end

%% --- 新增：局部函数 - 保存功率热图 ---
function savePowerHeatmap(turbine_positions, turbine_powers_kw, iteration, particle_index, save_directory, boundary)
    try
        % 创建不可见的图形窗口
        fig = figure('Visible', 'off', 'Position', [100, 100, 800, 600]); % 指定尺寸
        ax = axes(fig);
        hold(ax, 'on');

        % 绘制边界 (如果提供)
        if exist('boundary', 'var') && ~isempty(boundary)
            plot(ax, boundary(:,1), boundary(:,2), 'k--', 'LineWidth', 0.5);
        end

        % 转换功率单位为 MW
        turbine_powers_mw = turbine_powers_kw / 1000;

        % 绘制散点图作为热图
        scatter(ax, turbine_positions(:,1), turbine_positions(:,2), 50, turbine_powers_mw, 'filled'); % 增大点尺寸

        % 添加颜色条和标签
        cb = colorbar(ax);
        ylabel(cb, '功率 (MW)');
        colormap(ax, 'jet'); % 使用 jet 色图

        % 设置坐标轴
        axis(ax, 'equal');

        % 根据边界或数据范围设置坐标轴限制
        if exist('boundary', 'var') && ~isempty(boundary)
            min_x = min(boundary(:,1)) - 100; max_x = max(boundary(:,1)) + 100;
            min_y = min(boundary(:,2)) - 100; max_y = max(boundary(:,2)) + 100;
            xlim(ax, [min_x, max_x]);
            ylim(ax, [min_y, max_y]);
        else
            % 如果没有边界，基于风机位置自动调整，并增加一些边距
            min_x = min(turbine_positions(:,1)) - 100; max_x = max(turbine_positions(:,1)) + 100;
            min_y = min(turbine_positions(:,2)) - 100; max_y = max(turbine_positions(:,2)) + 100;
            xlim(ax, [min_x, max_x]);
            ylim(ax, [min_y, max_y]);
        end

        % 关闭坐标轴刻度和框
        axis(ax, 'off');

        % 添加标题
        title(ax, sprintf('迭代 %d - 粒子 %d 功率热图', iteration, particle_index), 'Visible', 'on');

        % 构建文件名和路径
        filename = sprintf('power_heatmap_iter%d_particle%d.png', iteration, particle_index);
        full_save_path = fullfile(save_directory, filename);

        % 保存图形
        saveas(fig, full_save_path);

        % 关闭图形窗口
        close(fig);

        % fprintf('已保存功率热图: %s\n', full_save_path); % 在 parfor 中避免过多输出

    catch err
        % 在实际函数内处理错误，而不是在调用处
        fprintf('警告: 功率热图生成/保存失败 (Iter %d, Particle %d): %s\n', iteration, particle_index, err.message);
    end
end