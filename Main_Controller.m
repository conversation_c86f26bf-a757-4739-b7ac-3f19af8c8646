%% Main_Controller.m - 风机阵列布局与系泊系统优化集成方案的主控制文件
% 该文件负责调用各模块并控制整体优化流程
% 作为整个系统的入口点，调用PSO优化、系泊优化、风机分类和系泊受力分析

function Main_Controller()
    %% Main_Controller - 风场布局与系泊系统主控制器
    %
    % 该函数整合了风场布局优化和系泊系统设计的主要流程
    % 包括风场布局优化、锚点布置、系泊力计算和成本分析等功能
    %

    % 修改: 2024-05-20 - 改进系泊成本计算和日志功能

    % 全局绘图控制
    enable_plotting = true; % 设置为 true 启用实时绘图显示
    enable_plot_saving = false; % 设置为 false 禁用图形保存到文件

    % 全局日志级别设置
    global LOG_LEVEL;
    global depth;
    global displacement_penalty_coefficient safety_factor_penalty_coefficient;

    LOG_LEVEL = 'info';  % 可选: 'error', 'warning', 'info', 'debug'

    % 初始化全局惩罚系数
    displacement_penalty_coefficient = 1.0; % 位移惩罚系数
    safety_factor_penalty_coefficient = 1.0; % 安全系数惩罚系数

    %% 常量定义和默认值配置
    % 货币单位常量
    CURRENCY_USD = 1;                 % 基准单位: 美元
    CURRENCY_USD_MILLION = 1e6;       % 百万美元
    CURRENCY_RMB = 7.0;               % 人民币汇率(估计值)
    CURRENCY_RMB_TENTHOUSAND = 1e4;   % 万元(人民币)

    % 系泊系统默认参数
    DEFAULT_CHAIN_DIAMETER = 120;     % 默认锚链直径(mm)

    DEFAULT_NUM_TURBINES = 30;        % 默认风机数量
    DEFAULT_LINES_PER_TURBINE = 3;    % 每个风机的系泊线数量
    DEFAULT_LINE_DEPTH_RATIO = 3;     % 系泊线长度与水深比率
    DEFAULT_ANCHOR_COST = 0.5;        % 默认单个锚点成本(百万美元)
    DEFAULT_CHAIN_COST = 0.3;         % 默认单根系泊线成本(百万美元)
    DEFAULT_CHAIN_COST_COEF = 0.055;  % 系泊链成本系数(元/mm^2/m)

    %% 主程序开始
    fprintf('========== 风电场优化与系泊分析程序启动 ==========\n');
    fprintf('当前时间: %s\n\n', datestr(now));

    %% 初始化全局参数
    % 水深参数
    depth = 200;                      % 水深 [m]
    DEFAULT_WATER_DEPTH = depth;        % 默认水深(m)
    % 风机位置与锚点设置
    rFair = 20;                       % fairlead 半径 [m]
    zFair = -21;                      % fairlead 的 z 坐标 [m]
    % 锚点角度设置
    angles_mat = deg2rad([60, 180, 300]);  % 各系泊线方向（转换为弧度）

    % 目标位移设置
    target_displacement = 0.12 * depth;   % 总位移极限为水深的12% [m]
    fprintf('目标位移极限: %.2f m (水深的12%%)\n', target_displacement);

    % 施加恒定外力
    constant_force = 8e5;  % 恒定外力大小 [N]

    % 测试方向
    num_directions = 12;  % 测试的方向数量
    test_angles = linspace(0, 2*pi*(num_directions-1)/num_directions, num_directions);  % 12个均匀分布的方向

    % 主风向设置
    main_wind_dir = 0;  % 主风向角度（弧度）

    % 定义实际场地边界
    A_boundary = [0,0; 6000,1200; 9000,4800; 7200,9000; 3000,7200; 0,3600];

    % 风机布满参数
    min_turbine_distance = 899;  % 风机最小间距 [m]

    % 多风向计算设置
    use_multi_direction = true;  % 是否启用多风向计算

    % 锚点最危险受力分析风向设置（每30度一个方向，共12个方向）
    anchor_analysis_directions = 0:30:330;  % 锚点受力分析的风向角度(度)

    % 说明：锚点最危险受力计算是指通过分析多个风向下的系泊系统受力情况，
    % 对每个锚点分别识别出其所受最大合力的情况，并基于这个"合成最坏情况"
    % 进行成本计算和安全性评估。这种方法比单一风向下的分析更保守且更符合
    % 实际工况，因为海上风机在不同时期会面临来自不同方向的风和波浪。

    % 风参数设置
    wind_params = struct(...
        'main_wind_dir', main_wind_dir, ...        % 主风向 [rad]
        'main_wind_speed', 1, ...                 % 主风向风速 [m/s]
        'rotor_diameter', 180, ...                 % 风轮直径 [m]
        'power_curve', [], ...                     % 功率曲线（将在后面初始化）
        'ct_curve', []  );                           % 推力系数曲线（将在后面初始化）


    % 初始化风机功率曲线和推力系数曲线（数据来源于buju3232.m）
    wind_speeds = [4, 4.5, 5, 5.5, 6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13, 13.5, 14, 14.5, 15, 15.5, 16, 16.5, 17, 17.5, 18, 18.5, 19, 19.5, 20, 20.5, 21, 21.5, 22, 22.5, 23, 23.5, 24, 24.5, 25]; % 43 points
    power_values = [110, 350, 600, 850, 1140, 1490, 1900, 2370, 2900, 3500, 4155, 4870, 5630, 6420, 7150, 7610, 7865, 7940, 7970, 8000, 8000, 8000, 8000, 8000, 8000, 8000, 8000, 8000, 8000, 8000, 8000, 8000, 8000, 8000, 8000, 8000, 8000, 8000, 8000, 8000, 8000, 8000, 8000]; % 43 points, values in kW
    ct_values = [0.92, 0.88, 0.85, 0.83, 0.82, 0.81, 0.8, 0.79, 0.78, 0.77, 0.76, 0.75, 0.73, 0.71, 0.67, 0.6, 0.52, 0.45, 0.39, 0.34, 0.3, 0.27, 0.24, 0.22, 0.19, 0.18, 0.16, 0.15, 0.14, 0.13, 0.12, 0.11, 0.1, 0.09, 0.09, 0.08, 0.08, 0.07, 0.07, 0.06, 0.06, 0.06, 0.05]; % 43 points

    % 确保风速、功率和推力系数数组长度一致
    fprintf('检查数组长度: wind_speeds=%d, power_values=%d, ct_values=%d\n', length(wind_speeds), length(power_values), length(ct_values));

    % 找出最短数组的长度
    min_length = min([length(wind_speeds), length(power_values), length(ct_values)]);

    % 如果数组长度不一致，截取所有数组到最短长度
    if length(wind_speeds) ~= min_length || length(power_values) ~= min_length || length(ct_values) ~= min_length
        fprintf('数组长度不一致，调整到共同长度: %d\n', min_length);
        wind_speeds = wind_speeds(1:min_length);
        power_values = power_values(1:min_length);
        ct_values = ct_values(1:min_length);
        fprintf('调整后数组长度: wind_speeds=%d, power_values=%d, ct_values=%d\n', length(wind_speeds), length(power_values), length(ct_values));
    end

    wind_params.power_curve = [wind_speeds', power_values'];
    wind_params.ct_curve = [wind_speeds', ct_values'];

    % 添加最小风机间距到wind_params结构体中
    wind_params.min_turbine_distance = min_turbine_distance;
    fprintf('添加min_turbine_distance=%.2f m到wind_params结构体\n', min_turbine_distance);

    % 如果启用多风向计算，添加多风向相关参数
    if use_multi_direction
        %% 多风向风场功率计算参数
        % ----------------------------------------------------------------------------
        % 多风向计算考虑真实风场中风向、风速和概率分布的影响，提高风场功率预测的准确性
        %
        % 参数说明:
        % wind_directions - 风向角度数组(弧度)，包含16个方向，每22.5度一个
        % wind_probs - 各风向概率分布，总和为1，反映各风向出现的频率
        % multi_wind_speeds - 各风向对应的平均风速(m/s)
        %
        % 多风向计算流程:
        % 1. WindFarmLayoutOptimizer检测到多风向参数后，自动切换到多风向计算模式
        % 2. 分别计算每个风向下的风场功率和效率
        % 3. 根据各风向的概率进行加权，得到综合功率和效率
        % 4. 返回is_multi_direction=true的结果结构体，包含direction_powers等字段
        % ----------------------------------------------------------------------------

        % 定义16个风向（每22.5度一个）- 使用区间中点代表
        wind_directions = deg2rad([11.25, 33.75, 56.25, 78.75, 101.25, 123.75, 146.25, 168.75, 191.25, 213.75, 236.25, 258.75, 281.25, 303.75, 326.25, 348.75]);  % 16个风向的中点

        % 定义各风向概率 - 从用户提供的数据转换而来
        wind_probs = [0.0410, 0.0395, 0.0346, 0.0370, 0.0353, 0.0418, 0.0627, 0.0851, 0.0855, 0.0951, 0.1083, 0.1306, 0.0711, 0.0405, 0.0462, 0.0457]; % 概率已转换为小数

        % 定义各风向风速 - 从用户提供的数据获取
        multi_wind_speeds = [8.95, 6.96, 6.97, 7.94, 8.04, 7.95, 8.89, 10.20, 10.22, 10.72, 10.93, 11.49, 9.52, 9.12, 10.33, 10.63];

        % 验证输入数据的有效性
        if length(wind_directions) ~= length(wind_probs) || length(wind_directions) ~= length(multi_wind_speeds)
            fprintf('警告: 多风向参数数组长度不匹配! directions=%d, probs=%d, speeds=%d\n', ...
                length(wind_directions), length(wind_probs), length(multi_wind_speeds));
        end

        % 验证风向概率总和
        prob_sum = sum(wind_probs);
        if abs(prob_sum - 1.0) > 0.001
            fprintf('警告: 风向概率总和不为1.0 (sum=%.3f)，已自动归一化\n', prob_sum);
            wind_probs = wind_probs / prob_sum;
        end

        % 添加到wind_params结构体中
        wind_params.wind_directions = wind_directions;
        wind_params.wind_probs = wind_probs;
        wind_params.wind_speeds = multi_wind_speeds;

        fprintf('启用多风向计算: %d个风向\n', length(wind_directions));
    else
        fprintf('使用单风向计算，主风向: %.1f°\n', rad2deg(main_wind_dir)); %#ok<*UNRCH>
    end

    % MOPSO参数设置
    mopso_params = struct(...
        'swarm_size', 20, ...       % 粒子数量
        'max_iterations', 30, ...    % 最大迭代次数
        'c1', 2.0, ...              % 个体学习因子
        'c2', 2.0, ...              % 群体学习因子
        'w_start', 0.9, ...         % 初始惯性权重
        'w_end', 0.4, ...           % 最终惯性权重
        'elite_count', 3 ...        % 每代用于系泊优化的精英粒子数量
    );

    % 注意：增加粒子数量和迭代次数将提高优化效果，但也会增加计算时间
    fprintf('MOPSO参数设置：粒子数量=%d, 最大迭代次数=%d, 精英粒子数=%d\n', ...
        mopso_params.swarm_size, mopso_params.max_iterations, mopso_params.elite_count);

    % MOPSO多目标优化配置
    mopso_config = struct(...
        'populationSize', mopso_params.swarm_size, ... % 粒子数量
        'maxIterations', mopso_params.max_iterations, ... % 最大迭代次数
        'archiveSize', 100, ... % 存档最大容量
        'objectiveWeights', [0.7, 0.3]); % 功率和成本的权重（分别对应功率最大化和成本最小化）

    fprintf('MOPSO配置：存档容量=%d, 权重=[%.1f, %.1f]\n', ...
        mopso_config.archiveSize, mopso_config.objectiveWeights(1), mopso_config.objectiveWeights(2));

    % 网格参数范围
    grid_params_range = struct(...
        'length_min', 900, ...      % 最小网格长度 [m]
        'length_max', 1800, ...     % 最大网格长度 [m]
        'rotation_min', 0, ...      % 最小旋转角 [rad]
        'rotation_max', pi/6 ...    % 最大旋转角 [rad]
    );

    % 系泊优化参数
    mooring_params = struct(...
        'min_contact_length', 0.125*depth, ...  % 最小海床接触长度 [m]
        'max_contact_length', 3*depth, ...      % 最大海床接触长度 [m]
        'contact_length_step', 5, ...           % 海床接触长度调整步长 [m]
        'min_chain_diameter', 80, ...           % 最小锚链直径 [mm]
        'max_chain_diameter', 220, ...          % 最大锚链直径 [mm]
        'chain_diameter_step', 1, ...           % 锚链直径调整步长 [mm]
        'typeName', "chain1", ...               % 系泊线类型标识字符串
        'use_generation_coupling', false ...     % 是否使用基于代际的PSO耦合优化
    );

    % 添加调试输出，确认锚链直径范围设置
    fprintf('【调试】Main_Controller中的系泊参数设置:\n');
    fprintf('  - 锚链直径范围: [%.1f, %.1f] mm\n', mooring_params.min_chain_diameter, mooring_params.max_chain_diameter);
    fprintf('  - 代际耦合: %s\n', conditional_str(mooring_params.use_generation_coupling, '启用', '禁用'));
    fprintf('  - 海床接触长度范围: [%.1f, %.1f] m\n', mooring_params.min_contact_length, mooring_params.max_contact_length);

    % 添加PSO适应度比例系数
    pso_upper_weight = 1.0;  % 上层PSO适应度比例系数
    pso_lower_weight = 1.0;  % 下层系泊优化适应度比例系数

    % 更新全局变量以匹配适应度比例系数
    displacement_penalty_coefficient = pso_lower_weight;
    safety_factor_penalty_coefficient = pso_lower_weight;

    fprintf('  - PSO适应度比例系数: 上层 = %.2f, 下层 = %.2f\n', pso_upper_weight, pso_lower_weight);
    fprintf('  - 已更新全局罚函数系数: 位移 = %.2f, 安全系数 = %.2f\n', displacement_penalty_coefficient, safety_factor_penalty_coefficient);

    % 网格优化到系泊优化失败反馈控制
    max_grid_adjustments = 5;  % 最大网格调整次数
    grid_adjustment_factor = 0.9;  % 网格长度调整因子

    % 系泊力计算参数
    mooring_force_params = struct(...
        'pretension', 1000000, ...        % 预张力 (N)，使用默认值
        'stiffness', 1000, ...            % 刚度 (N/m)，使用默认值
        'environmentalLoad', [constant_force, 0], ... % 环境载荷 [Fx, Fy] (N)
        'safetyFactor', 1.5 ...           % 安全系数
    );

    % 独立锚点参数
    dedicated_anchor_params = struct(...
        'use_triangle_vertices', true, ...     % 使用三角形顶点作为锚点
        'enable_vertex_sharing', true, ...     % 允许不同风机共享相同三角形顶点作为锚点
        'calculate_distance', true, ...        % 计算风机到三角形的距离
        'max_safety_factor', 2.0 ...           % 最大安全系数
    );

    % 共享锚点参数 (保留但不使用，以保持兼容性)
    shared_anchor_params = struct(...
        'distance_threshold', 0, ...          % 设置为0，禁用共享
        'min_contribution', 999, ...          % 设置为大值，禁用共享
        'max_angle_diff', 0 ...               % 设置为0，禁用共享
    );

    %% 初始化数据管理器
    data_manager = DataManager();

    %% 尝试设置Python环境和MoorPy可用性
    try
        % 检查util目录并添加到路径
        if ~exist('util', 'dir')
            mkdir('util');
            addpath('util');
            fprintf('创建并添加util目录到路径\n');
        else
            addpath('util');
            fprintf('添加现有util目录到路径\n');
        end

        % 检查是否已创建MoorPy工具函数
        if exist('initializeMoorPy.m', 'file') && exist('createMoorPySystem.m', 'file') && exist('evaluateMoorPySystem.m', 'file')
            fprintf('检测到MoorPy相关工具函数\n');

            % 尝试初始化MoorPy
            try
                [mp, np, mp_success, mp_error] = initializeMoorPy();
                if mp_success
                    fprintf('MoorPy初始化成功，将用于系统计算\n');
                else
                    fprintf('MoorPy初始化失败: %s\n', mp_error);
                    fprintf('将使用备选模拟方法\n');
                end
            catch mp_init_error
                fprintf('MoorPy初始化过程出错: %s\n', mp_init_error.message);
                fprintf('将使用备选模拟方法\n');
            end
        else
            fprintf('未找到MoorPy工具函数，将使用备选模拟方法\n');
        end
    catch env_error
        fprintf('环境设置出错: %s\n', env_error.message);
        fprintf('将继续使用现有设置\n');
    end

    %% MOPSO优化网格参数
    fprintf('开始优化网格参数...\n');

    % 优化使用try-catch包装，确保即使失败也能继续
    try
        % 设置是否显示实时图形
        show_plots = true; % 启用实时图形显示

        % 确保MOPSO目录在路径中
        if exist('MOPSO', 'dir')
            addpath(genpath('MOPSO'));
            fprintf('已添加MOPSO目录到路径\n');
        else
            error('未找到MOPSO目录，无法继续优化');
        end

        fprintf('使用多目标PSO(MOPSO)优化网格参数...\n');

        % 执行多目标优化
        [grid_params, mopso_history] = MOPSO_GridOptimizer(...
            mopso_params, grid_params_range, depth, rFair, zFair, angles_mat, ...
            target_displacement, constant_force, test_angles, num_directions, ...
            mooring_params.typeName, main_wind_dir, max_grid_adjustments, ...
            grid_adjustment_factor, mooring_params, A_boundary, wind_params, ...
            pso_upper_weight, pso_lower_weight, show_plots, use_multi_direction, anchor_analysis_directions, enable_plot_saving);

        % 保存多目标优化结果
        if ~exist('results', 'dir')
            mkdir('results');
        end
        save('results/mopso_results.mat', 'grid_params', 'mopso_history');
        fprintf('多目标优化结果已保存到results/mopso_results.mat\n');

        % 使用DataManager专门的MOPSO结果保存方法
        data_manager.saveMopsoResults(grid_params, mopso_history);

        % 输出优化结果
        fprintf('优化完成，最优网格参数:\n');
        fprintf('  网格长度: %.2f m\n', grid_params.length);
        fprintf('  网格旋转角: %.4f rad (%.2f°)\n', grid_params.rotation, rad2deg(grid_params.rotation));

        % MOPSO的可视化已经在MOPSO_GridOptimizer中完成
        fprintf('MOPSO优化过程可视化已在优化过程中完成\n');
    catch pso_error
        fprintf('优化失败: %s\n', pso_error.message);
        fprintf('将使用默认网格参数\n');

        % 使用默认网格参数
        grid_params = struct(...
            'length', 1000, ...   % 默认网格长度 1000m
            'rotation', pi/12 ... % 默认旋转角 15度
        );
        mopso_history = [];
    end

    % 根据优化后的网格长度计算锚点半径
    rAnchor = calculateAnchorRadius(grid_params.length);
    fprintf('  根据网格长度计算的锚点半径: %.2f m\n', rAnchor);

    %% 多目标优化结果分析
    fprintf('\n分析多目标优化结果...\n');

    % 获取Pareto前沿
    if ~isempty(mopso_history) && isfield(mopso_history(end), 'pareto_front')
        pareto_front = mopso_history(end).pareto_front;

        % 显示多目标优化结果
        fprintf('--- 多目标优化结果 ---\n');
        fprintf('Pareto前沿解数量: %d\n', size(pareto_front, 1));

        % 显示选择的最优解信息
        if isfield(mopso_history(end), 'complete_analysis') && ...
           isfield(mopso_history(end).complete_analysis, 'total_power') && ...
           isfield(mopso_history(end).complete_analysis, 'total_mooring_cost')
            fprintf('选择的最优解:\n');
            fprintf('  - 功率: %.2f MW\n', mopso_history(end).complete_analysis.total_power/1e6);
            fprintf('  - 锚泊成本: %.2f M$\n', mopso_history(end).complete_analysis.total_mooring_cost);
            fprintf('  - 风机数量: %d\n', mopso_history(end).complete_analysis.num_turbines);
            fprintf('  - 锚点数量: %d\n', mopso_history(end).complete_analysis.num_anchors);
            fprintf('  - 最小安全系数: %.2f\n', mopso_history(end).complete_analysis.min_safety_factor);
        end
    else
        fprintf('警告: 多目标优化未生成有效的Pareto前沿\n');
    end

    %% 使用最优网格参数进行系泊系统优化
    fprintf('\n开始系泊系统优化...\n');

    % 系泊优化使用try-catch包装，确保即使失败也能继续
    try
        % 注释掉单独的系泊优化步骤，直接使用PSO优化结果中的系泊参数
        % [mooring_status, mooring_history] = MooringOptimizer(grid_params, ...
        %     depth, rFair, zFair, rAnchor, angles_mat, target_displacement, ...
        %     constant_force, test_angles, num_directions, mooring_params);

        fprintf('注意: 跳过单独的系泊系统优化，直接使用MOPSO过程中的最优系泊参数\n');

        % 从MOPSO历史中获取最佳系泊参数
        if isempty(mopso_history) || ~isfield(mopso_history(end), 'complete_analysis')
            error('无法从MOPSO结果中获取系泊参数，MOPSO历史为空或缺少完整分析');
        end

        % 提取最后一代的最优解
        g_best_details = mopso_history(end).complete_analysis;

        if ~isstruct(g_best_details) || ~isfield(g_best_details, 'mooring_params_optimized')
            error('MOPSO历史中缺少必要的系泊优化参数');
        end

        % 提取系泊优化状态
        mooring_status = g_best_details.mooring_params_optimized;

        if ~isfield(mooring_status, 'success')
            mooring_status.success = true; % 默认标记为成功
        end

        % 确保有锚链长度信息
        if ~isfield(mooring_status, 'chain_length') || mooring_status.chain_length <= 0
            error('MOPSO结果中的系泊参数缺少有效的锚链长度');
        end

        % 创建模拟的mooring_history（如需要）
        mooring_history = struct();
        mooring_history.iterations = 1;
        mooring_history.chain_diameters = mooring_status.chain_diameter;
        mooring_history.contact_lengths = mooring_status.contact_length;
        mooring_history.displacements = mooring_status.max_displacement;
        mooring_history.costs = mooring_status.cost;
        mooring_history.safety_factors = g_best_details.min_safety_factor;

        % 存储系泊优化结果
        data_manager.saveMooringResults(mooring_status, mooring_history);

        % 输出系泊参数
        fprintf('使用MOPSO优化过程中的最优系泊参数:\n');
        fprintf('  海床接触长度: %.2f m\n', mooring_status.contact_length);
        fprintf('  锚链直径: %.2f mm\n', mooring_status.chain_diameter);
        fprintf('  最大位移: %.2f m (%.2f%% 水深)\n', ...
            mooring_status.max_displacement, ...
            mooring_status.max_displacement/depth*100);
        fprintf('  安全系数: %.2f\n', g_best_details.min_safety_factor);
        fprintf('  锚链长度: %.2f m\n', mooring_status.chain_length);
        fprintf('  相对成本: %.2f\n', mooring_status.cost);

    catch mooring_error
        % 直接重新抛出错误，中止程序
        error('无法使用MOPSO优化过程中的系泊参数，程序终止: %s', mooring_error.message);
    end

end

function avg_length = calculateAverageChainLength(turbine_positions, anchorPositions, anchorMap)
    % 计算系泊链的平均长度
    % 输入:
    %   turbine_positions - n x 2 矩阵，每行表示一个风机的[x,y]坐标
    %   anchorPositions   - m x 2 矩阵，每行表示一个锚点的[x,y]坐标
    %   anchorMap         - n x 3 矩阵，表示每个风机连接的三个锚点索引
    %
    % 输出:
    %   avg_length - 所有系泊链的平均长度

    % 初始化总长度
    total_length = 0;
    count = 0;

    % 计算所有系泊链的长度
    num_turbines = size(turbine_positions, 1);
    for i = 1:num_turbines
        turbine_pos = turbine_positions(i, :);

        % 获取该风机连接的三个锚点
        for j = 1:3
            anchor_idx = anchorMap(i, j);
            if anchor_idx > 0 % 确保锚点索引有效
                anchor_pos = anchorPositions(anchor_idx, :);

                % 计算距离
                chain_length = norm(turbine_pos - anchor_pos);
                total_length = total_length + chain_length;
                count = count + 1;
            end
        end
    end

    % 计算平均长度
    if count > 0
        avg_length = total_length / count;
    else
        avg_length = 0;
    end
end
function rAnchor = calculateAnchorRadius(grid_length)
    % 根据网格长度计算锚点半径，设置为网格长度的0.577倍
    rAnchor = 0.577 * grid_length;
    fprintf('计算锚点半径: %.2f m (网格长度 %.2f m 的0.577倍)\n', rAnchor, grid_length);
end

%% 条件字符串生成函数 - 用于格式化输出
function str = conditional_str(condition, true_str, false_str)
    if condition
        str = true_str;
    else
        str = false_str;
    end
end

